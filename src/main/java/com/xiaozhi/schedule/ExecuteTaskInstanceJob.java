package com.xiaozhi.schedule;

import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.communication.common.SessionManager;
import com.xiaozhi.communication.domain.TaskChain;
import com.xiaozhi.communication.server.mqtt.MqttServerPublish;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.dao.TaskInstanceMapper;
import com.xiaozhi.dialogue.llm.ChatService;
import com.xiaozhi.dialogue.service.DialogueService;
import com.xiaozhi.dialogue.tts.factory.TtsServiceFactory;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.enums.TaskContent;
import com.xiaozhi.enums.TaskStatus;
import com.xiaozhi.utils.OpusProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class ExecuteTaskInstanceJob {

    @Resource
    private ManagerMapper managerMapper;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Resource
    private ChatService chatService;

    @Resource
    private SessionManager sessionManager;

    @Resource
    private DialogueService dialogueService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MqttServerPublish mqttServerPublish;

    @Resource
    private TtsServiceFactory ttsServiceFactory;

    @Resource
    private OpusProcessor opusProcessor;

    private final String TaskDelayQueue = "xiaozhi:task:queue";

    // 使用虚拟线程池处理定时任务
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(
            Runtime.getRuntime().availableProcessors(),
            Thread.ofVirtual().name("audio-scheduler-", 0).factory());

    // Lua 脚本：原子地取出并删除 0-now 之间的数据
    private final String ZRangeScript = """
            local msgs = redis.call('ZRANGEBYSCORE', KEYS[1], 0, ARGV[1], 'limit', 0, 100)
            if (#msgs > 0) then
            redis.call('ZREM', KEYS[1], unpack(msgs))
            return msgs
            else return {} end
            """;

    @Scheduled(fixedDelay = 20 * 1000)
    public void test() throws InterruptedException, IOException {
        var deviceId = "74:4d:bd:7f:2f:98";
        var topic = STR."devices/p2p/GID@@@\{deviceId.replaceAll(":", "_")}";

        mqttServerPublish.wakeup(topic);

        TimeUnit.SECONDS.sleep(2);

        var newSession = sessionManager.getSessionByDeviceId(deviceId);

        var ttsService = ttsServiceFactory.getTtsService(newSession.getCurrentRole().getTtsConfig(), newSession.getCurrentRole().getVoice());

        var text = "Hi, what are you doing?";
        newSession.sendTTSStart();
        newSession.sendSentenceStart(text);


        var bytes = opusProcessor.readOpus(new File("/Users/<USER>/Downloads/s32.opus"));
        log.info("frames {}", bytes.size());

        var startTime = System.nanoTime();

        for (var i = 0; i < 3; i ++) {
            newSession.sendBinaryMessage(bytes.get(i));
        }

        test(newSession, startTime, bytes, 3);


        // var flux = DataBufferUtils.read(
        //                 new FileSystemResource("/Users/<USER>/Downloads/zhu.opus"),
        //                 new DefaultDataBufferFactory(),
        //                 960
        //         )
        //         .map(buffer -> {
        //             byte[] bytes = new byte[buffer.readableByteCount()];
        //             buffer.read(bytes);
        //             DataBufferUtils.release(buffer);
        //             return bytes;
        //         });



        // try (InputStream fin = Files.newInputStream(Paths.get("/Users/<USER>/Downloads/s32.opus"))) {
        //     stream(fin)
        //             .subscribe(f -> {
        //                 log.info("send bytes {}", f.length);
        //                 newSession.sendBinaryMessage(f);
        //             });
        //     Thread.sleep(120_000); // 演示挂起
        // } catch (IOException e) {
        //     throw new RuntimeException(e);
        // }


//         ttsService.streamTextToSpeech(text)
//                 .asFlux()
//                 // .map(it -> opusProcessor.pcmToOpus(newSession.getSessionId(), it, true))
//                 .subscribe(bytes -> {
//                     log.info("opus bytes {}", bytes.length);

// //                    for (var b : bytes) {
// //                        newSession.sendBinaryMessage(b);
// //                    }
//                     newSession.sendBinaryMessage(bytes);

//                 }, Throwable::printStackTrace, newSession::sendTTSStop);
    }

    private void test(ChatSession session, long startTime, List<byte[]> frames, int idx) {
        Runnable frameTask = () -> {
            try {
                session.sendBinaryMessage(frames.get(idx));

                var nextFrameIndex = idx + 1;
                if (nextFrameIndex < frames.size()) {
                    test(session, startTime, frames, nextFrameIndex);
                } else {
                    // session.sendTTSStop();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        };

        next(startTime, idx, frameTask);
    }

    private void next(long startTime, int idx, Runnable frameTask) {
        // 直接用帧索引计算预期发送时间（纳秒级精度）
        long expectedTime = startTime + idx * 60 * 1_000_000;
        long currentTime = System.nanoTime();
        long delayNanos = expectedTime - currentTime;

        ScheduledFuture<?> future;
        if (delayNanos <= 0) {
            // 如果当前时间已经超过预期时间，立即发送
            scheduler.schedule(frameTask, 0, TimeUnit.NANOSECONDS);
        } else {
            // 延迟到精确时间点再发送
            scheduler.schedule(frameTask, delayNanos, TimeUnit.NANOSECONDS);
        }
    }

    @Scheduled(cron = "0 */1 * * * ?")
    public void run() {
        var maxRange = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        var taskJsonArray = Optional.ofNullable(stringRedisTemplate.opsForZSet().rangeByScore(TaskDelayQueue, 0, maxRange))
                .orElseGet(Set::of);

        log.info("From {} to {} with zone {} get tasks {}", 0, maxRange, ZoneId.systemDefault(), taskJsonArray);

        var tasks = taskJsonArray.stream()
                .map(Integer::valueOf)
                .toList();

        for (var taskId : tasks) {
            var instance = taskInstanceMapper.selectById(taskId);
            if (instance == null) continue;
            if (instance.getStatus() != TaskStatus.Waiting) continue;

            var now = Instant.now();

            // create task chain
            var durations = new ArrayList<Integer>();
            for (int i = 0; i < instance.getDuration().length(); i += 2) {
                durations.add(Integer.parseInt(instance.getDuration().substring(i, i + 2)));
            }

            var idx = new AtomicInteger(0);
            var dummy = new TaskChain();
            Arrays.stream(TaskContent.values())
                    .filter(it -> (it.getValue() & instance.getContent()) != 0)
                    .map(it -> new TaskChain().setInstanceId(instance.getId()).setContent(it).setStartTime(now).setDuration(durations.get(idx.getAndIncrement())))
                    .reduce(dummy, (z, x) -> {
                        z.setNext(x);
                        return x;
                    }, (_, it) -> it);

            var taskChain = dummy.getNext();
            var topic = STR."devices/p2p/GID@@@\{instance.getDeviceId().replaceAll(":", "_")}";

            var session = sessionManager.getSessionByDeviceId(instance.getDeviceId());
            if (session != null) continue;

            switch (instance.getType()) {
                case Listening, Information -> {
                    mqttServerPublish.open(topic, "player", "news");
                    sessionManager.putListeningStopTime(instance.getDeviceId(), Instant.now().plusSeconds(taskChain.getDuration() * 60));
                    instance.setStatus(TaskStatus.Running);
                    taskInstanceMapper.updateById(instance);
                    // remove when task started
                    stringRedisTemplate.opsForZSet().remove(TaskDelayQueue, taskId.toString());
                }
                case Bedtime, Conversation -> {
                    // wakeup
                    // if (session == null || !session.isOpen()) {
                    // }
                    mqttServerPublish.wakeup(topic);
                    Thread.startVirtualThread(() -> {
                        try {
                            TimeUnit.SECONDS.sleep(2);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                        var newSession = sessionManager.getSessionByDeviceId(instance.getDeviceId());
                        if (newSession == null) return;
                        newSession.setTaskChain(taskChain);

                        if (newSession.isPlaying()) {
                            // 设备正在播放，将任务置为取消
                            instance.setStatus(TaskStatus.Canceled);
                            taskInstanceMapper.updateById(instance);
                            return;
                        }

                        var managerQuery = new OhMyLambdaQueryWrapper<Manager>()
                                .eq(Manager::getId, instance.getManagerId())
                                .select(Manager::getId, Manager::getName, Manager::getCefr);
                        var manager = managerMapper.selectOne(managerQuery);

                        var hello = chatService.getStartSentence(manager.getName(), manager.getCefr(), "【】");
                        dialogueService.sendSentenceWithoutSegmentation(newSession, hello, true)
                                .thenRun(() -> {
                                    instance.setStatus(TaskStatus.Running);
                                    taskInstanceMapper.updateById(instance);
                                    // remove when task started
                                    stringRedisTemplate.opsForZSet().remove(TaskDelayQueue, taskId.toString());
                                });
                    });

                }
            }

        }
    }

    public static Flux<byte[]> stream(InputStream in) {
        return Flux.<byte[]>create(emitter -> {
                    Schedulers.boundedElastic().schedule(() -> {
                        try {
                            byte[] buf = new byte[4096]; // 读缓存
                            int pos = 0, read;
                            while ((read = in.read(buf, pos, buf.length - pos)) != -1) {
                                pos += read;
                                int off = 0;
                                while (true) {
                                    int sz = frameSize(buf, off, pos - off);
                                    if (sz <= 0) break;          // 需要更多数据
                                    if (off + sz > pos) break;   // 帧不完整
                                    byte[] frame = new byte[sz];
                                    System.arraycopy(buf, off, frame, 0, sz);
                                    emitter.next(frame);
                                    off += sz;
                                }
                                // 把剩余字节搬到头部
                                if (off < pos) {
                                    System.arraycopy(buf, off, buf, 0, pos - off);
                                }
                                pos = pos - off;
                            }
                            emitter.complete();
                        } catch (Throwable e) {
                            emitter.error(e);
                        }
                    });
                })
                .delayElements(Duration.ofMillis(60)); // 60 ms 节拍

}

    /** 返回这一帧占多少字节，-1 表示数据不够 */
    public static int frameSize(byte[] buf, int off, int len) {
        if (len < 1) return -1;
        int toc = buf[off] & 0xFF;
        int c  = toc & 3;
        int s  = (toc >> 3) & 1;
        int len0, n;
        switch (c) {
            case 0:  n = 1; len0 = 1; break;
            case 1:  n = 1; len0 = 2; break;
            case 2:  n = 1 + s; len0 = 2; break;
            default: /* c==3 */
                if (len < 2) return -1;
                int m = buf[off + 1] & 0x3F;
                n = m + 1;
                len0 = 2 + (s != 0 ? n : 0);
        }
        // 简单实现：只支持单帧（c=0/1/2），不支持 packet-per-frame c=3
        if (c == 3) throw new IllegalArgumentException("multi-frame packet not implemented");
        return len0;
    }
}
