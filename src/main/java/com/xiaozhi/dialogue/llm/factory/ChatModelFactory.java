package com.xiaozhi.dialogue.llm.factory;

import com.xiaozhi.dialogue.domain.ModelParams;
import com.xiaozhi.dialogue.llm.ChatModelService;
import com.xiaozhi.dialogue.llm.providers.CozeChatModel;
import com.xiaozhi.dialogue.llm.providers.DifyChatModel;
import com.xiaozhi.dialogue.llm.tool.XiaoZhiToolCallingManager;
import com.xiaozhi.dialogue.token.factory.TokenServiceFactory;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.service.SysConfigService;

import java.net.http.HttpClient;
import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.MetadataMode;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.model.NoopApiKey;
import org.springframework.ai.model.SimpleApiKey;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.retry.RetryUtils;
import org.springframework.ai.zhipuai.ZhiPuAiChatModel;
import org.springframework.ai.zhipuai.ZhiPuAiChatOptions;
import org.springframework.ai.zhipuai.api.ZhiPuAiApi;
import org.springframework.http.client.JdkClientHttpRequestFactory;
import org.springframework.http.client.reactive.JdkClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * ChatModel工厂
 * 根据配置的模型ID，创建对应的ChatModel
 */
@Slf4j
@Component
public class ChatModelFactory {

    @Resource
    private SysConfigService configService;

    @Resource
    private TokenServiceFactory tokenService;

    private final ConcurrentMap<String, ChatModelService> chatModelMap = new ConcurrentHashMap<>();

    public ChatModelService takeChatModel(SysConfig config, String sessionId, String deviceId) {
        return chatModelMap.computeIfAbsent(STR."LLM_\{deviceId}", _ -> createChatModel(config, sessionId, deviceId));
    }

    public ChatModelService takeVisionModel() {
        return takeModelFor("vision");
    }

    public ChatModelService takeIntentModel() {
        return takeModelFor("intent");
    }

    public ChatModelService takeModelFor(String modelType) {
        var config = configService.selectModelType(modelType);
        Assert.notNull(config, STR."未配置\{modelType}模型");
        return createChatModel(config);
    }

    private ChatModelService createChatModel(SysConfig config) {
        return this.createChatModel(config, null, null);
    }

    /**
     * 创建ChatModel
     *
     * @param config
     * @return
     */
    private ChatModelService createChatModel(SysConfig config, String sessionId, String deviceId) {
        var params = new ModelParams()
                .setEndpoint(config.getApiUrl())
                .setApiKey(config.getApiKey())
                .setApiSecret(config.getApiSecret())
                .setModel(config.getName())
                .setTopP(0.7)
                .setTemperature(0.9)
                .setDeviceId(deviceId)
                .setSessionId(sessionId);
        String provider = config.getProvider().toLowerCase();
        String model = config.getName();
        String endpoint = config.getApiUrl();
        String apiKey = config.getApiKey();
        Double temperature = 0.7;
        Double topP = 0.9;

        return switch (provider) {
            case "ollama" -> newOllamaChatModel(endpoint, model, temperature, topP);
            case "zhipu" -> newZhiPuChatModel(endpoint, apiKey, model, temperature, topP);
            case "dify" -> new DifyChatModel(endpoint, config.getApiKey());
            case "coze" -> new ChatModelService(new CozeChatModel(params));
            default -> newOpenAiChatModel(endpoint, apiKey, model, temperature, topP);
        };
    }

    private ChatModelService newOllamaChatModel(String endpoint, String model, Double temperature, Double topP) {
        var ollamaApi = OllamaApi.builder().baseUrl(endpoint).build();

        var ollamaAiChatOptions = OllamaOptions.builder()
                .model(model)
                .temperature(temperature)
                .topP(topP)
                .build();

        var chatModel = OllamaChatModel.builder()
                .ollamaApi(ollamaApi)
                .defaultOptions(ollamaAiChatOptions)
                .toolCallingManager(XiaoZhiToolCallingManager.builder().build())
                .build();

        log.info("Using Ollama model: {}", model);

        return new ChatModelService(chatModel);
    }

    private ChatModelService newOpenAiChatModel(String endpoint, String apiKey, String model, Double temperature, Double topP) {
        var headers = new LinkedMultiValueMap<String, String>();
        headers.add("Content-Type", "application/json");

        // LM Studio不支持Http/2，所以需要强制使用HTTP/1.1
        var openAiApi = OpenAiApi.builder()
                .apiKey(StringUtils.hasText(apiKey) ? new SimpleApiKey(apiKey) : new NoopApiKey())
                .baseUrl(endpoint)
                .completionsPath("/chat/completions")
                .headers(headers)
                .webClientBuilder(WebClient.builder()
                        // Force HTTP/1.1 for streaming
                        .clientConnector(new JdkClientHttpConnector(HttpClient.newBuilder()
                                .version(HttpClient.Version.HTTP_1_1)
                                .connectTimeout(Duration.ofSeconds(30))
                                .build())))
                .restClientBuilder(RestClient.builder()
                        // Force HTTP/1.1 for non-streaming
                        .requestFactory(new JdkClientHttpRequestFactory(HttpClient.newBuilder()
                                .version(HttpClient.Version.HTTP_1_1)
                                .connectTimeout(Duration.ofSeconds(30))
                                .build())))
                .build();

        var openAiChatOptions = OpenAiChatOptions.builder()
                .model(model)
                .temperature(temperature)
                .topP(topP)
                .build();

        var chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .toolCallingManager(XiaoZhiToolCallingManager.builder().build())
                .build();

        log.info("Using OpenAi model: {}", model);

        return new ChatModelService(chatModel);
    }

    private ChatModelService newZhiPuChatModel(String endpoint, String apiKey, String model, Double temperature, Double topP) {
        var zhiPuAiApi = new ZhiPuAiApi(endpoint, apiKey);

        var zhipuAiChatOptions = ZhiPuAiChatOptions.builder()
                .model(model)
                .temperature(temperature)
                .topP(topP)
                .build();

        var chatModel = new ZhiPuAiChatModel(zhiPuAiApi, zhipuAiChatOptions);

        log.info("Using zhiPu model: {}", model);

        return new ChatModelService(chatModel);
    }

    private EmbeddingModel newOpenAiEmbeddingModel(String endpoint, String apiKey, String model) {
        var headers = new LinkedMultiValueMap<String, String>();
        headers.add("Content-Type", "application/json");

        var openAiApi = OpenAiApi.builder()
                .apiKey(StringUtils.hasText(apiKey) ? new SimpleApiKey(apiKey) : new NoopApiKey())
                .baseUrl(endpoint)
                .headers(headers)
                .webClientBuilder(WebClient.builder()
                        // Force HTTP/1.1 for streaming
                        .clientConnector(new JdkClientHttpConnector(HttpClient.newBuilder()
                                .version(HttpClient.Version.HTTP_1_1)
                                .connectTimeout(Duration.ofSeconds(30))
                                .build())))
                .restClientBuilder(RestClient.builder()
                        // Force HTTP/1.1 for non-streaming
                        .requestFactory(new JdkClientHttpRequestFactory(HttpClient.newBuilder()
                                .version(HttpClient.Version.HTTP_1_1)
                                .connectTimeout(Duration.ofSeconds(30))
                                .build())))
                .build();

        var embeddingModel = new OpenAiEmbeddingModel(
                openAiApi,
                MetadataMode.EMBED,
                OpenAiEmbeddingOptions.builder()
                        .model(model)
                        .encodingFormat("float")
                        .build(),
                RetryUtils.DEFAULT_RETRY_TEMPLATE
        );

        return embeddingModel;
    }
}
