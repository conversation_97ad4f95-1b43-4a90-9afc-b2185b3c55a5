package com.xiaozhi.dialogue.tts.providers;

import com.microsoft.cognitiveservices.speech.*;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;
import com.microsoft.cognitiveservices.speech.audio.AudioOutputStream;
import com.microsoft.cognitiveservices.speech.audio.PushAudioOutputStreamCallback;
import com.xiaozhi.dialogue.tts.Text2SpeechParams;
import com.xiaozhi.dialogue.tts.TtsService;
import com.xiaozhi.entity.SysConfig;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Sinks;

import java.io.ByteArrayOutputStream;
import java.net.URI;
import java.nio.file.Paths;
import java.util.concurrent.ExecutionException;

@Slf4j
public class AzureService implements TtsService {
    private static final String PROVIDER_NAME = "azure";

    private final String url;
    private final String secret;
    private final String voice;
    private final String outputPath;

    public AzureService(SysConfig config, String voice, String outputPath) {
        this.url = config.getApiUrl();
        this.secret = config.getApiSecret(); // 使用 apiSecret 存储 region
        this.voice = voice;
        this.outputPath = outputPath;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String audioFormat() {
        return "wav";
    }

    @Override
    public boolean isSupportStreamTts() {
        return true;
    }

    @Override
    public String textToSpeech(Text2SpeechParams params) throws Exception {
        if (params.getText() == null || params.getText().isEmpty()) {
            log.warn("文本内容为空！");
            return null;
        }

        var filepath = Paths.get(outputPath, getAudioFileName()).toString();

        try (var speechConfig = SpeechConfig.fromEndpoint(new URI(url), secret);
             var audioConfig = AudioConfig.fromWavFileOutput(filepath)) {

            speechConfig.setSpeechSynthesisVoiceName(voice);

            // 构建SSML以支持语速调节
            var ssml = buildSSML("en-US", params.getText(), params.getSpeed());
            // var ssml = buildSSML("zh-CN", params.getText(), params.getSpeed());

            try (var synthesizer = new SpeechSynthesizer(speechConfig, audioConfig)) {
                var result = synthesizer.SpeakSsml(ssml);

                if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
                    log.info("Azure TTS 合成完成，文件保存至: {}", filepath);
                } else if (result.getReason() == ResultReason.Canceled) {
                    var cancellation = SpeechSynthesisCancellationDetails.fromResult(result);
                    log.error("Azure TTS 合成被取消: {}", cancellation.getReason());
                    if (cancellation.getReason() == CancellationReason.Error) {
                        log.error("Azure TTS 错误代码: {}, 错误详情: {}",
                                cancellation.getErrorCode(), cancellation.getErrorDetails());
                        throw new Exception("Azure TTS 合成失败: " + cancellation.getErrorDetails());
                    }
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("Azure TTS 合成异常", e);
            throw new Exception("Azure TTS 合成异常: " + e.getMessage(), e);
        }

        return filepath;
    }

    @Override
    public Sinks.Many<byte[]> streamTextToSpeech(String text) {
        if (text == null || text.isEmpty()) {
            log.warn("文本内容为空！");
            return null;
        }

        // 创建音频数据流
        Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();

        // 在后台线程中执行流式TTS
        Thread.startVirtualThread(() -> {
            try {
                streamSynthesis(text, audioSink, 1.0); // 默认语速
            } catch (Exception e) {
                log.error("Azure 流式TTS合成失败", e);
                audioSink.tryEmitError(e);
            }
        });

        return audioSink;
    }

    private void streamSynthesis(String text, Sinks.Many<byte[]> audioSink, double speed) throws Exception {
        try (var speechConfig = SpeechConfig.fromEndpoint(new URI(url), secret)) {
            speechConfig.setSpeechSynthesisVoiceName(voice);
            speechConfig.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Ogg16Khz16BitMonoOpus);

            // 创建推送音频输出流回调
            var callback = new PushAudioOutputStreamCallback() {
                private final ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                private static final int TARGET_CHUNK_SIZE = 960; // 目标块大小：960字节

                @Override
                public int write(byte[] dataBuffer) {
                    if (dataBuffer != null && dataBuffer.length > 0) {
                        try {
                            // 将数据写入缓冲区
                            buffer.write(dataBuffer);

                            // 检查缓冲区是否有足够的数据发送960字节的块
                            while (buffer.size() >= TARGET_CHUNK_SIZE) {
                                byte[] bufferedData = buffer.toByteArray();

                                // 提取960字节的数据块
                                byte[] chunk = new byte[TARGET_CHUNK_SIZE];
                                System.arraycopy(bufferedData, 0, chunk, 0, TARGET_CHUNK_SIZE);

                                // 发送960字节的数据块
                                audioSink.tryEmitNext(chunk);
                                log.info("发送音频数据块: {} 字节", chunk.length);

                                // 更新缓冲区，移除已发送的数据
                                int remainingSize = bufferedData.length - TARGET_CHUNK_SIZE;
                                buffer.reset();
                                if (remainingSize > 0) {
                                    buffer.write(bufferedData, TARGET_CHUNK_SIZE, remainingSize);
                                }
                            }
                        } catch (Exception e) {
                            log.error("处理音频数据时发生错误", e);
                        }
                    }
                    return dataBuffer != null ? dataBuffer.length : 0;
                }

                @Override
                public void close() {
                    try {
                        // 发送缓冲区中剩余的数据（如果有的话）
                        if (buffer.size() > 0) {
                            byte[] remainingData = buffer.toByteArray();
                            // 如果剩余数据不足960字节，也发送出去
                            audioSink.tryEmitNext(remainingData);
                            log.info("发送最后的音频数据块: {} 字节", remainingData.length);
                        }
                        buffer.close();
                    } catch (Exception e) {
                        log.error("关闭音频流时发生错误", e);
                    }
                    log.info("音频流关闭");
                    audioSink.tryEmitComplete();
                }
            };

            // 创建推送音频输出流
            AudioOutputStream pushStream = AudioOutputStream.createPushStream(callback);
            AudioConfig audioConfig = AudioConfig.fromStreamOutput(pushStream);

            // 构建SSML以支持语速调节
            var ssml = buildSSML("en-US", text, speed);

            try (var synthesizer = new SpeechSynthesizer(speechConfig, audioConfig)) {
                // 开始异步合成
                var result = synthesizer.SpeakSsmlAsync(ssml).get();

                if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
                    log.info("Azure 流式TTS合成完成");
                } else if (result.getReason() == ResultReason.Canceled) {
                    var cancellation = SpeechSynthesisCancellationDetails.fromResult(result);
                    log.error("Azure 流式TTS合成被取消: {}", cancellation.getReason());
                    if (cancellation.getReason() == CancellationReason.Error) {
                        log.error("Azure TTS 错误代码: {}, 错误详情: {}",
                                cancellation.getErrorCode(), cancellation.getErrorDetails());
                        throw new Exception("Azure 流式TTS合成失败: " + cancellation.getErrorDetails());
                    }
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("Azure 流式TTS合成异常", e);
            throw new Exception("Azure 流式TTS合成异常: " + e.getMessage(), e);
        }
    }

    private String buildSSML(String lang, String text, double speed) {
        var clampedSpeed = Math.max(0.5, Math.min(2.0, speed));

        return STR."""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="\{lang}">
                <voice name="\{voice}">
                    <prosody rate="\{clampedSpeed}">\{text}</prosody>
                </voice>
            </speak>""";
    }

}
