package com.xiaozhi.communication.common;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xiaozhi.communication.domain.RoleWithConfig;
import com.xiaozhi.communication.server.mqtt.MqttServerPublish;
import com.xiaozhi.dao.TaskInstanceMapper;
import com.xiaozhi.dialogue.llm.tool.ToolsSessionHolder;
import com.xiaozhi.dialogue.service.DialogueService;
import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.entity.SysRole;
import com.xiaozhi.entity.TaskInstance;
import com.xiaozhi.enums.ListenMode;
import com.xiaozhi.enums.TaskStatus;
import com.xiaozhi.event.ChatSessionCloseEvent;
import com.xiaozhi.event.SessionCloseRequestEvent;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket会话管理服务
 * 负责管理所有WebSocket连接的会话状态
 * 使用JDK 21虚拟线程实现异步处理
 * TODO 重构计划：可能没必要作为Service由Spring管理，而是由Handler处理。
 * TODO 实际底层驱动力来自于Handler，后续服务都是基于Session而不需要SessionManager的。
 */
@Service
public class SessionManager implements ApplicationListener<SessionCloseRequestEvent> {
    private static final Logger logger = LoggerFactory.getLogger(SessionManager.class);

    // 设置不活跃超时时间为60秒
    private static final long INACTIVITY_TIMEOUT_SECONDS = 60;

    // 用于存储所有连接的会话信息
    private final ConcurrentHashMap<String, ChatSession> sessions = new ConcurrentHashMap<>();

    // 添加 deviceId 到 sessionId 的映射
    private final ConcurrentHashMap<String, String> deviceToSession = new ConcurrentHashMap<>();

    // 听力任务的停止时间
    private final ConcurrentHashMap<String, Instant> listeningStopTime = new ConcurrentHashMap<>();

    // 定时任务执行器
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    private final ScheduledExecutorService checkTaskScheduler = Executors.newSingleThreadScheduledExecutor();

    private final ScheduledExecutorService listeningTaskScheduler = Executors.newSingleThreadScheduledExecutor();

    @Resource
    private DialogueService dialogueService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Resource
    private MqttServerPublish mqttServerPublish;

    /**
     * 初始化方法，启动定时检查不活跃会话的任务
     */
    @PostConstruct
    public void init() {
        // 每10秒检查一次不活跃的会话
        scheduler.scheduleAtFixedRate(this::checkInactiveSessions, 10, 10, TimeUnit.SECONDS);
        logger.info("不活跃会话检查任务已启动，超时时间: {}秒", INACTIVITY_TIMEOUT_SECONDS);

        // 每10秒检查一次设备任务
        checkTaskScheduler.scheduleAtFixedRate(this::taskChainProcessor, 10, 10, TimeUnit.SECONDS);

        listeningTaskScheduler.scheduleAtFixedRate(this::listeningTaskProcessor, 10, 10, TimeUnit.SECONDS);
    }

    /**
     * 销毁方法，关闭定时任务执行器
     */
    @PreDestroy
    public void destroy() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("不活跃会话检查任务已关闭");
    }

    /**
     * 处理会话关闭请求事件
     * 用于解决与DialogueService的循环依赖问题
     */
    @Override
    public void onApplicationEvent(SessionCloseRequestEvent event) {
        String sessionId = event.getSessionId();
        String reason = event.getReason();

        logger.debug("收到会话关闭请求事件 - SessionId: {}, Reason: {}", sessionId, reason);

        // 执行会话关闭逻辑
        closeSession(sessionId);
    }

    /**
     * 检查不活跃的会话并关闭它们
     * 使用虚拟线程实现异步处理
     */
    private void checkInactiveSessions() {
        Thread.startVirtualThread(() -> {
            Instant now = Instant.now();
            sessions.values().forEach(session -> {
                Instant lastActivity = session.getLastActivityTime();
                if (lastActivity != null) {
                    Duration inactiveDuration = Duration.between(lastActivity, now);
                    if (inactiveDuration.getSeconds() > INACTIVITY_TIMEOUT_SECONDS) {
                        logger.info("会话 {} 已经 {} 秒没有有效活动，自动关闭", session.getSessionId(), inactiveDuration.getSeconds());
                        closeSession(session);
                    }
                }
            });
        });
    }

    /**
     * 处理多功能类型的任务
     */
    private void taskChainProcessor() {
        Thread.startVirtualThread(() -> {
            sessions.values().forEach(session -> {
                logger.info("Checking task chain..... {}", session.getSessionId());
                if (session.getTaskChain() != null) {
                    var startTime = session.getTaskChain().getStartTime();
                    var duration = session.getTaskChain().getDuration();

                    var taskDuration = Duration.between(startTime, Instant.now()).getSeconds();
                    logger.info("Task duration: {}s", taskDuration);
                    // speaking task completed
                    if (taskDuration >= duration * 60 && !session.isPlaying()) {
                        // start listening task
                        dialogueService.sendSentenceWithoutSegmentation(session, "和你聊天真开心！现在，让我们放松一下，一起进入今天的科普小故事环节吧~", false)
                                .thenRun(() -> {
                                    var deviceId = session.getSysDevice().getDeviceId();
                                    var topic = STR."devices/p2p/GID@@@\{deviceId.replaceAll(":", "_")}";
                                    mqttServerPublish.send(topic, """
                                            { "type": "navigate", "target": "player", "action": "open", "category": "story" }
                                            """);
                                    // 定时关闭
                                    listeningStopTime.putIfAbsent(deviceId, Instant.now().plusSeconds(session.getTaskChain().getDuration() * 60));
                                })
                                .thenRun(() -> {
                                    // all tasks completed
                                    if (session.getTaskChain().getNext() == null) {
                                        taskInstanceMapper.update(
                                                new LambdaUpdateWrapper<TaskInstance>()
                                                        .eq(TaskInstance::getId, session.getTaskChain().getInstanceId())
                                                        .set(TaskInstance::getStatus, TaskStatus.Finished)
                                        );
                                    }
                                    session.setTaskChain(session.getTaskChain().getNext());
                                })
                                .exceptionally(e -> {
                                    logger.error("处理对话失败: {}", e.getMessage(), e);
                                    return null;
                                });
                    }
                }
            });
        });
    }

    private void listeningTaskProcessor() {
        listeningStopTime.forEach((deviceId, timeToStop) -> {
            // time reached
            if (timeToStop.isBefore(Instant.now())) {
                var topic = STR."devices/p2p/GID@@@\{deviceId.replaceAll(":", "_")}";
                mqttServerPublish.send(topic, """
                        { "type": "navigate", "target": "player", "action": "close" }
                        """);
                listeningStopTime.remove(deviceId);
            }
        });
    }


    /**
     * 更新会话的最后有效活动时间
     * 这个方法应该只在检测到实际的用户活动时调用，如语音输入或明确的交互
     *
     * @param sessionId 会话ID
     */
    public void updateLastActivity(String sessionId) {
        ChatSession session = sessions.get(sessionId);
        if (session != null) {
            session.setLastActivityTime(Instant.now());
        }
    }

    /**
     * 注册新的会话
     *
     * @param chatSession 会话
     */
    public void registerSession(ChatSession chatSession) {
        sessions.put(chatSession.getSessionId(), chatSession);
        logger.info("会话已注册 - SessionId: {}  SessionType: {}", chatSession.getSessionId(), chatSession.getClass().getSimpleName());
    }

    /**
     * 关闭并清理WebSocket会话
     *
     * @param sessionId 会话ID
     */
    public void closeSession(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            closeSession(chatSession);
        }
    }

    /**
     * 关闭并清理 ChatSession
     */
    public void closeSession(ChatSession chatSession) {
        try {
            if (chatSession.getTaskChain() != null && chatSession.getTaskChain().getNext() != null) {
                var nextTask = chatSession.getTaskChain().getNext();
                nextTask.setStartTime(Instant.now());
                chatSession.setTaskChain(nextTask);
                // continue task
                chatSession.sendTextMessage("""
                        {
                            "type": "navigate",
                            "target": "player",
                            "action": "open",
                            "category": "story"
                        }
                        """);
                return;
            }

            //if (!chatSession.isSilentConnected()) {
            // 清理设备映射
            var device = chatSession.getSysDevice();
            if (device != null && device.getDeviceId() != null) {
                deviceToSession.remove(device.getDeviceId());
            }

            // 清理会话映射
            sessions.remove(chatSession.getSessionId());
            //}

            // 关闭会话
            chatSession.close();

            // 清理音频流
            Sinks.Many<byte[]> sink = chatSession.getAudioSinks();
            if (sink != null) {
                sink.tryEmitComplete();
            }

            applicationContext.publishEvent(new ChatSessionCloseEvent(chatSession));

            logger.info("会话已关闭 - SessionId: {} SessionType: {}", chatSession.getSessionId(), chatSession.getClass().getSimpleName());
        } catch (Exception e) {
            logger.error("清理会话资源时发生错误 - SessionId: {}", chatSession.getSessionId(), e);
        }
    }

    /**
     * 清理全部会话
     */
    public int clearSessions() {
        sessions.values()
                .parallelStream()
                .forEach(this::closeSession);
        return sessions.size();
    }

    /**
     * 注册设备配置
     *
     * @param sessionId 会话ID
     * @param device    设备信息
     */
    public void registerDevice(String sessionId, SysDevice device) {
        var session = sessions.get(sessionId);

        if (session != null && device != null && device.getDeviceId() != null) {
            session.setSysDevice(device);
            deviceToSession.put(device.getDeviceId(), sessionId);
            updateLastActivity(sessionId);
            logger.debug("设备配置已注册 - SessionId: {}, DeviceId: {}", sessionId, device.getDeviceId());
        }
    }

    /**
     * 设置会话完成后是否关闭
     *
     * @param sessionId 会话ID
     * @param close     是否关闭
     */
    public void setCloseAfterChat(String sessionId, boolean close) {
        logger.info("Func exit executing");
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setCloseAfterChat(close);
        }
    }

    /**
     * 获取会话完成后是否关闭
     *
     * @param sessionId 会话ID
     * @return 是否关闭
     */
    public boolean isCloseAfterChat(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.isCloseAfterChat();
        } else {
            return true;
        }
    }

//    /**
//     * 缓存配置信息
//     *
//     * @param configId 配置ID
//     * @param config   配置信息
//     */
//    public void cacheConfig(Integer configId, SysConfig config) {
//        if (configId != null && config != null) {
//            configCache.put(configId, config);
//        }
//    }
//
//    /**
//     * 删除配置
//     *
//     * @param configId 配置ID
//     */
//    public void removeConfig(Integer configId) {
//        configCache.remove(configId);
//    }

    /**
     * 获取会话
     *
     * @param sessionId 会话ID
     * @return WebSocket会话
     */
    public ChatSession getSession(String sessionId) {
        return sessions.get(sessionId);
    }

    /**
     * 获取会话
     *
     * @param deviceId 设备ID
     * @return 会话ID
     */
    public ChatSession getSessionByDeviceId(String deviceId) {
        var sessionId = deviceToSession.get(deviceId);
        return sessionId == null ? null : sessions.get(sessionId);
    }

    /**
     * 获取设备配置
     *
     * @param sessionId 会话ID
     * @return 设备配置
     */
    public SysDevice getDeviceConfig(String sessionId) {
        var session = sessions.get(sessionId);
        return session == null ? null : session.getSysDevice();
    }

    /**
     * 获取会话的function holder
     *
     * @param sessionId 会话ID
     * @return FunctionSessionHolder
     */
    public ToolsSessionHolder getFunctionSessionHolder(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.getFunctionSessionHolder();
        }
        return null;
    }

    /**
     * 获取用户的可用角色列表
     *
     * @param sessionId 会话ID
     * @return 角色列表
     */
    public List<SysRole> getAvailableRoles(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.getSysRoleList();
        }
        return null;
    }

    /**
     * 音乐播放状态
     *
     * @param sessionId 会话ID
     * @param isPlaying 是否正在播放音乐
     */
    public void setMusicPlaying(String sessionId, boolean isPlaying) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setMusicPlaying(isPlaying);
        }
    }

    /**
     * 是否在播放音乐
     *
     * @param sessionId 会话ID
     * @return 是否正在播放音乐
     */
    public boolean isMusicPlaying(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.isMusicPlaying();
        }
        return false;
    }

    /**
     * 播放状态
     *
     * @param sessionId 会话ID
     * @param isPlaying 是否正在说话
     */
    public void setPlaying(String sessionId, boolean isPlaying) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setPlaying(isPlaying);
        }
    }

    /**
     * 是否在播放音乐
     *
     * @param sessionId 会话ID
     * @return 是否正在播放音乐
     */
    public boolean isPlaying(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.isPlaying();
        }
        return false;
    }

    /**
     * 设备状态
     *
     * @param sessionId
     * @param mode      设备状态 auto/realTime
     */
    public void setMode(String sessionId, ListenMode mode) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setMode(mode);
        }
    }

    /**
     * 获取设备状态
     *
     * @param sessionId
     */
    public ListenMode getMode(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.getMode();
        }
        return ListenMode.Auto;
    }

    /**
     * 设置流式识别状态
     *
     * @param sessionId   会话ID
     * @param isStreaming 是否正在流式识别
     */
    public void setStreamingState(String sessionId, boolean isStreaming) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setStreamingState(isStreaming);
        }
        updateLastActivity(sessionId); // 更新活动时间
    }

    /**
     * 获取流式识别状态
     *
     * @param sessionId 会话ID
     * @return 是否正在流式识别
     */
    public boolean isStreaming(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.isStreamingState();
        }
        return false;
    }

    /**
     * 创建音频数据流
     *
     * @param sessionId 会话ID
     */
    public void createAudioStream(String sessionId) {
        Sinks.Many<byte[]> sink = Sinks.many().multicast().onBackpressureBuffer();
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setAudioSinks(sink);
        }
    }

    /**
     * 获取音频数据流
     *
     * @param sessionId 会话ID
     * @return 音频数据流
     */
    public Sinks.Many<byte[]> getAudioStream(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.getAudioSinks();
        }
        return null;
    }

    /**
     * 发送音频数据
     *
     * @param sessionId 会话ID
     * @param data      音频数据
     */
    public void sendAudioData(String sessionId, byte[] data) {
        Sinks.Many<byte[]> sink = getAudioStream(sessionId);
        if (sink != null) {
            sink.tryEmitNext(data);
        }
    }

    /**
     * 完成音频流
     *
     * @param sessionId 会话ID
     */
    public void completeAudioStream(String sessionId) {
        Sinks.Many<byte[]> sink = getAudioStream(sessionId);
        if (sink != null) {
            sink.tryEmitComplete();
        }
    }

    /**
     * 关闭音频流
     *
     * @param sessionId 会话ID
     */
    public void closeAudioStream(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setAudioSinks(null);
        }
    }

    // ==================== 角色管理 ====================

    /**
     * 更新会话的当前角色缓存
     *
     * @param sessionId 会话ID
     * @param role      新的角色对象
     */
    public void updateCurrentRole(String sessionId, SysRole role) {
        var session = sessions.get(sessionId);
        if (session != null) {
            var roleWithConfig = new RoleWithConfig().setRole(role);
            session.setCurrentRole(roleWithConfig);
            logger.debug("已更新会话角色缓存 - sessionId: {}, roleId: {}, roleName: {}", sessionId, role.getId(), role.getName());
        }
    }

    /**
     * 清除会话的角色缓存（角色切换时使用）
     *
     * @param sessionId 会话ID
     */
    public void clearCurrentRole(String sessionId) {
        var session = sessions.get(sessionId);
        if (session != null) {
            session.clearCurrentRole();
            logger.debug("已清除会话角色缓存 - sessionId: {}", sessionId);
        }
    }

    /**
     * 清除所有使用指定角色的会话缓存（角色更新时使用）
     *
     * @param roleId 角色ID
     */
    public void clearCurrentRoleByRoleId(Integer roleId) {
        if (roleId == null) {
            return;
        }

        int clearedCount = 0;
        for (var session : sessions.values()) {
            var device = session.getSysDevice();
            if (device != null && roleId.equals(device.getRoleId())) {
                session.clearCurrentRole();
                clearedCount++;
            }
        }

        if (clearedCount > 0) {
            logger.debug("已清除 {} 个会话的角色缓存 - roleId: {}", clearedCount, roleId);
        }
    }

    public void putListeningStopTime(String deviceId, Instant stopTime) {
        listeningStopTime.putIfAbsent(deviceId, stopTime);
    }
}
